import http from '@/utils/http';

const API_BASE = '/ihmp/api/app-template';

/**
 * 应用模板服务
 */
export const appTemplateService = {
  /**
   * 分页查询应用模板列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageSize 每页大小
   * @param {string} params.name 应用名称
   * @param {number} params.appType 应用类型 (0:开发工具, 1:大模型, 2:开源应用, 3:帆一产品)
   * @param {number} params.status 状态 (1:启用, 0:禁用)
   * @param {number} params.isOfficial 是否官方应用 (1:是, 0:否)
   * @param {string} params.keyword 关键词搜索
   * @param {string} params.sortType 排序方式 (recommend/latest/download/name)
   * @param {string} params.tag 标签
   */
  getAppTemplates: (params = {}) => {
    return http.get(`${API_BASE}/page`, { params });
  },

  /**
   * 根据ID查询应用模板详情
   * @param {number} id 应用模板ID
   */
  getAppTemplateById: (id) => {
    return http.get(`${API_BASE}/${id}`);
  },

  /**
   * 新增应用模板
   * @param {Object} data 应用模板数据
   */
  createAppTemplate: (data) => {
    return http.post(API_BASE, data);
  },

  /**
   * 更新应用模板
   * @param {Object} data 应用模板数据
   */
  updateAppTemplate: (data) => {
    return http.put(API_BASE, data);
  },

  /**
   * 批量删除应用模板
   * @param {Array} ids 应用模板ID数组
   */
  deleteAppTemplates: (ids) => {
    return http.delete(API_BASE, { data: ids });
  },

  /**
   * 切换应用状态
   * @param {number} id 应用模板ID
   * @param {number} status 状态 (1:启用, 0:禁用)
   */
  toggleStatus: (id, status) => {
    return http.post(`${API_BASE}/${id}/toggle-status`, null, {
      params: { status }
    });
  },

  /**
   * 批量切换应用状态
   * @param {Array} ids 应用模板ID数组
   * @param {number} status 状态 (1:启用, 0:禁用)
   */
  batchToggleStatus: (ids, status) => {
    const params = new URLSearchParams();
    ids.forEach(id => params.append('ids', id));
    params.append('status', status);
    return http.post(`${API_BASE}/batch-toggle-status?${params.toString()}`);
  },

  /**
   * 增加下载次数
   * @param {number} id 应用模板ID
   */
  incrementDownload: (id) => {
    return http.post(`${API_BASE}/${id}/download`);
  },

  /**
   * 检查名称和版本唯一性
   * @param {string} name 应用名称
   * @param {string} version 版本号
   * @param {number} excludeId 排除的ID（更新时使用）
   */
  checkUnique: (name, version, excludeId = null) => {
    const params = { name, version };
    if (excludeId) params.excludeId = excludeId;
    return http.get(`${API_BASE}/check-unique`, { params });
  },

  /**
   * 复制应用模板
   * @param {number} id 应用模板ID
   * @param {string} newName 新名称
   * @param {string} newVersion 新版本号
   */
  copyAppTemplate: (id, newName, newVersion) => {
    return http.post(`${API_BASE}/${id}/copy`, null, {
      params: { newName, newVersion }
    });
  },

  /**
   * 获取应用类型下拉列表
   */
  getAppTypeOptions: () => {
    return http.get(`${API_BASE}/app-type-options`);
  },

  /**
   * 获取排序选项下拉列表
   */
  getSortOptions: () => {
    return http.get(`${API_BASE}/sort-options`);
  },

  /**
   * 获取应用类型统计
   */
  getStatistics: () => {
    return http.get(`${API_BASE}/statistics`);
  }
};

/**
 * 应用类型枚举
 */
export const APP_TYPES = {
  DEVELOPMENT_TOOL: 0,  // 开发工具
  AI_MODEL: 1,          // 大模型
  OPEN_SOURCE: 2,       // 开源应用
  FANYI_PRODUCT: 3      // 帆一产品
};

/**
 * 应用类型标签映射
 */
export const APP_TYPE_LABELS = {
  [APP_TYPES.DEVELOPMENT_TOOL]: '开发工具',
  [APP_TYPES.AI_MODEL]: '大模型',
  [APP_TYPES.OPEN_SOURCE]: '开源应用',
  [APP_TYPES.FANYI_PRODUCT]: '帆一产品'
};

/**
 * 排序类型枚举
 */
export const SORT_TYPES = {
  RECOMMEND: 'recommend',
  LATEST: 'latest',
  DOWNLOAD: 'download',
  NAME: 'name'
};

/**
 * 排序类型标签映射
 */
export const SORT_TYPE_LABELS = {
  [SORT_TYPES.RECOMMEND]: '推荐',
  [SORT_TYPES.LATEST]: '最新',
  [SORT_TYPES.DOWNLOAD]: '下载量',
  [SORT_TYPES.NAME]: '名称'
};
