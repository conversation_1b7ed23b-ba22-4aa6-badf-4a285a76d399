// 简单的测试文件，用于验证 appTemplateService 的请求处理是否正确

import { appTemplateService } from '@/services/appTemplateService';

// 模拟测试函数
const testAppTemplateService = async () => {
  console.log('开始测试应用模板服务...');
  
  try {
    // 测试获取应用模板列表
    console.log('测试获取应用模板列表...');
    const data = await appTemplateService.getAppTemplates({
      pageIndex: 1,
      pageSize: 10,
      status: 1
    });
    
    console.log('✅ 获取应用模板列表成功:', data);
    
    // 测试获取统计数据
    console.log('测试获取统计数据...');
    const stats = await appTemplateService.getStatistics();
    console.log('✅ 获取统计数据成功:', stats);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
};

// 导出测试函数
export { testAppTemplateService };

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，可以通过控制台调用 testAppTemplateService()
  window.testAppTemplateService = testAppTemplateService;
  console.log('测试函数已挂载到 window.testAppTemplateService，可在控制台调用');
}
